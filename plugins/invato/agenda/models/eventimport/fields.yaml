# ===================================
#  Form Field Definitions
# ===================================

fields:
    step1_section:
        label: "1. Upload an Import File"
        type: section

    file_format:
        label: File Format
        type: dropdown
        options:
            json: JSON
            csv: CSV
            csv_custom: CSV Custom
        span: right

    import_file:
        label: Import File
        type: fileupload
        mode: file
        span: left
        fileTypes: [csv, json]
        useCaption: false

    format_delimiter:
        label: Delimiter Character
        span: left
        trigger:
            action: show
            condition: value[csv_custom]
            field: file_format

    format_enclosure:
        label: Enclosure Character
        span: auto
        trigger:
            action: show
            condition: value[csv_custom]
            field: file_format

    format_escape:
        label: Escape Character
        span: auto
        trigger:
            action: show
            condition: value[csv_custom]
            field: file_format

    step2_section:
        label: "2. Match Columns"
        type: section

    step3_section:
        label: "3. Import Data"
        type: section
